import type {ComponentProps} from "react";

const AppScreenshotSkewed = ({...props}: ComponentProps<"svg">) => (
  <svg
    fill="none"
    height="737"
    viewBox="0 0 1280 737"
    width="1280"
    xmlns="http://www.w3.org/2000/svg"
    xmlnsXlink="http://www.w3.org/1999/xlink"
    {...props}
  >
    <g filter="url(#filter0_dddddd_3051_12573)">
      <g clipPath="url(#clip0_3051_12573)">
        <rect
          fill="url(#paint0_linear_3051_12573)"
          height="616"
          rx="12"
          transform="matrix(0.965926 -0.258819 0.707107 0.707107 59 344.583)"
          width="1115"
        />
        <g clipPath="url(#clip1_3051_12573)">
          <rect
            fill="white"
            fillOpacity="0.16"
            height="12"
            rx="6"
            transform="matrix(0.965926 -0.258819 0.707107 0.707107 92.4607 353.549)"
            width="12"
          />
          <rect
            fill="white"
            fillOpacity="0.16"
            height="12"
            rx="6"
            transform="matrix(0.965926 -0.258819 0.707107 0.707107 111.779 348.373)"
            width="12"
          />
          <rect
            fill="white"
            fillOpacity="0.16"
            height="12"
            rx="6"
            transform="matrix(0.965926 -0.258819 0.707107 0.707107 131.098 343.196)"
            width="12"
          />
          <g clipPath="url(#clip2_3051_12573)">
            <rect
              fill="white"
              fillOpacity="0.1"
              height="32"
              rx="8"
              transform="matrix(0.965926 -0.258819 0.707107 0.707107 483.351 239.844)"
              width="250"
            />
            <text
              dominantBaseline="middle"
              fill="white"
              fillOpacity="0.7"
              fontSize="13"
              textAnchor="middle"
              transform="matrix(0.965926 -0.258819 0.707107 0.707107 483.351 239.844)"
              x="125"
              y="16"
            >
              heroui.pro
            </text>
            <g clipPath="url(#clip3_3051_12573)">
              <path
                d="M510.491 251.698L515.352 250.396C516.141 250.184 516.243 249.767 515.62 249.144L512.927 246.451C512.38 245.904 511.755 245.687 511.071 245.793L510.168 244.889C508.766 243.487 506.775 243.101 505.358 243.481C503.942 243.86 503.402 244.925 504.803 246.326L505.707 247.23C505.145 247.458 505.122 247.849 505.669 248.396L508.362 251.089C508.985 251.712 509.702 251.909 510.491 251.698ZM505.705 245.998C504.812 245.105 505.11 244.409 506.038 244.161C506.962 243.913 508.236 244.188 509.129 245.08L510.086 246.038L506.662 246.955L505.705 245.998Z"
                fill="white"
                fillOpacity="0.6"
              />
            </g>
            <g clipPath="url(#clip4_3051_12573)">
              <path
                d="M722.991 192.111L723.218 191.247C722.493 191.383 721.86 191.353 721.252 191.19C719.596 190.746 718.601 189.752 718.856 188.801L719.359 186.922C719.616 185.966 720.968 185.603 722.625 186.047C724.296 186.498 725.276 187.485 725.021 188.436L724.761 189.41C725.244 189.663 725.669 190.017 725.892 190.362L726.332 188.742C726.709 187.354 725.273 185.91 722.842 185.259C720.406 184.603 718.445 185.139 718.073 186.526L717.55 188.5C717.178 189.886 718.609 191.331 721.04 191.983C721.614 192.133 722.244 192.196 722.991 192.111ZM719.997 188.485L719.776 189.348C720.495 189.213 721.138 189.246 721.736 189.406C723.402 189.852 724.392 190.843 724.132 191.795L723.628 193.674C723.372 194.63 722.025 194.991 720.363 194.549C718.696 194.102 717.712 193.111 717.972 192.158L718.227 191.186C717.749 190.931 717.323 190.583 717.096 190.234L716.662 191.853C716.285 193.241 717.725 194.688 720.156 195.34C722.587 195.991 724.549 195.455 724.92 194.069L725.442 192.101C725.815 190.708 724.379 189.265 721.952 188.618C721.379 188.461 720.75 188.399 719.997 188.485Z"
                fill="white"
                fillOpacity="0.6"
              />
            </g>
          </g>
          <g clipPath="url(#clip5_3051_12573)">
            <path
              d="M174.633 343.263L187.206 339.894C188.791 339.47 189.011 338.648 187.867 337.503L181.328 330.965C180.178 329.815 178.76 329.439 177.175 329.864L164.603 333.232C163.017 333.657 162.792 334.474 163.941 335.624L170.48 342.162C171.63 343.312 173.048 343.688 174.633 343.263ZM173.668 342.187C172.986 342.369 172.348 342.206 171.827 341.685L165.466 335.324C164.945 334.803 165.061 334.445 165.744 334.262L169.083 333.367L177.008 341.292L173.668 342.187ZM178.14 330.94C178.815 330.759 179.454 330.915 179.976 331.436L186.337 337.797C186.858 338.319 186.74 338.684 186.064 338.865L178.373 340.926L170.448 333.001L178.14 330.94ZM169.611 335.719C169.868 335.65 169.927 335.43 169.744 335.247C169.562 335.065 169.186 334.968 168.929 335.037L167.424 335.44C167.16 335.51 167.096 335.725 167.278 335.908C167.461 336.091 167.842 336.193 168.106 336.122L169.611 335.719ZM171.051 337.159C171.308 337.09 171.367 336.87 171.184 336.687C171.001 336.504 170.625 336.408 170.369 336.476L168.864 336.88C168.6 336.95 168.535 337.165 168.718 337.348C168.901 337.531 169.282 337.633 169.546 337.562L171.051 337.159ZM172.485 338.593C172.742 338.524 172.807 338.31 172.624 338.127C172.441 337.944 172.065 337.847 171.808 337.916L170.304 338.319C170.04 338.39 169.975 338.605 170.158 338.788C170.341 338.97 170.717 339.067 170.981 338.996L172.485 338.593Z"
              fill="white"
              fillOpacity="0.6"
            />
          </g>
          <g clipPath="url(#clip6_3051_12573)">
            <path
              d="M219.238 325.274C219.404 325.439 219.662 325.573 219.957 325.648L228.571 327.956C228.879 328.035 229.143 328.041 229.347 327.987C229.823 327.859 229.914 327.52 229.582 327.188C229.411 327.017 229.171 326.892 228.915 326.827L225.971 326.026L222.246 325.133L224.089 324.737L233.265 322.279C233.756 322.147 233.854 321.806 233.501 321.452C233.141 321.093 232.551 320.943 232.061 321.074L222.884 323.533L221.204 324.082L221.938 321.994L222.375 320.287C222.415 320.136 222.356 319.962 222.185 319.791C221.854 319.46 221.271 319.308 220.796 319.435C220.592 319.49 220.443 319.599 220.385 319.79L219.062 324.753C219.005 324.916 219.072 325.108 219.238 325.274Z"
              fill="white"
              fillOpacity="0.6"
            />
          </g>
          <g clipPath="url(#clip7_3051_12573)">
            <path
              d="M268.274 312.135C268.108 311.969 267.844 311.829 267.555 311.76L258.954 309.455C258.628 309.368 258.369 309.367 258.157 309.424C257.689 309.549 257.593 309.883 257.924 310.215C258.096 310.386 258.335 310.511 258.597 310.581L261.541 311.382L265.251 312.279L263.423 312.671L254.247 315.13C253.749 315.263 253.652 315.597 254.011 315.956C254.365 316.31 254.953 316.467 255.451 316.334L264.627 313.875L266.308 313.327L265.574 315.415L265.137 317.122C265.091 317.267 265.15 317.441 265.321 317.612C265.653 317.943 266.241 318.101 266.709 317.975C266.92 317.919 267.056 317.806 267.11 317.63L268.45 312.655C268.502 312.487 268.44 312.3 268.274 312.135Z"
              fill="white"
              fillOpacity="0.2"
            />
          </g>
          <g clipPath="url(#clip8_3051_12573)">
            <path
              d="M289.569 306.933C292.309 309.673 297.517 311.072 301.252 310.072C304.987 309.071 305.787 306.062 303.047 303.322C302.721 302.996 302.179 302.847 301.734 302.966C301.304 303.081 301.249 303.39 301.575 303.716C303.718 305.859 303.09 308.206 300.169 308.989C297.249 309.771 293.191 308.68 291.048 306.537C288.894 304.382 289.511 302.052 292.424 301.272C292.922 301.138 293.41 301.043 293.855 301L293.294 303.042C293.263 303.183 293.311 303.317 293.455 303.461C293.759 303.765 294.3 303.914 294.708 303.805C294.919 303.748 295.043 303.652 295.072 303.518L295.886 300.4C295.929 300.242 295.872 300.089 295.722 299.94C295.579 299.796 295.352 299.675 295.091 299.605L289.686 298.132C289.452 298.061 289.224 298.053 289.012 298.109C288.605 298.219 288.538 298.545 288.837 298.843C288.98 298.986 289.175 299.095 289.403 299.16L292.55 299.991C292.168 300.031 291.755 300.106 291.363 300.211C287.635 301.21 286.834 304.198 289.569 306.933Z"
              fill="white"
              fillOpacity="0.6"
            />
          </g>
          <g clipPath="url(#clip9_3051_12573)">
            <path
              d="M1118.8 83.9266C1119.09 84.2142 1119.66 84.3609 1120.04 84.2578L1125.23 82.8678L1129.03 86.6653C1129.31 86.9471 1129.87 87.1018 1130.26 86.9965C1130.65 86.8913 1130.74 86.5625 1130.46 86.2808L1126.66 82.4832L1131.84 81.0953C1132.23 80.9922 1132.32 80.6693 1132.03 80.3817C1131.75 80.0941 1131.18 79.9416 1130.79 80.0447L1125.61 81.4325L1121.82 77.6408C1121.54 77.3591 1120.97 77.2065 1120.58 77.3118C1120.18 77.4171 1120.1 77.7436 1120.39 78.0254L1124.18 81.8171L1118.99 83.2071C1118.6 83.3102 1118.52 83.639 1118.8 83.9266Z"
              fill="white"
              fillOpacity="0.6"
            />
          </g>
          <g clipPath="url(#clip10_3051_12573)">
            <path
              d="M1096.63 97.4586C1101.11 96.2598 1102.09 92.5496 1098.82 89.2803C1095.55 86.0051 1089.12 84.2881 1084.65 85.4869C1080.18 86.6835 1079.19 90.3878 1082.47 93.663C1085.74 96.9323 1092.17 98.6552 1096.63 97.4586ZM1095.63 96.4608C1091.85 97.4748 1086.61 96.0661 1083.84 93.2956C1081.07 90.5252 1081.86 87.4987 1085.65 86.4847C1089.43 85.4706 1094.69 86.8751 1097.46 89.6455C1100.23 92.4159 1099.42 95.4467 1095.63 96.4608ZM1087.66 88.4841C1087.31 88.5764 1087.24 88.8271 1087.5 89.0854L1090.43 92.0202L1091.73 93.2415L1090.23 92.7519L1088.57 92.3033C1088.38 92.2512 1088.18 92.2429 1088.02 92.288C1087.69 92.3761 1087.62 92.6246 1087.86 92.8653C1087.98 92.9886 1088.14 93.0813 1088.32 93.1297L1092.95 94.3608C1093.22 94.4352 1093.41 94.4515 1093.58 94.4064C1093.76 94.3592 1093.84 94.2618 1093.88 94.1094L1094.6 91.4453C1094.63 91.3411 1094.59 91.2183 1094.47 91.095C1094.23 90.8544 1093.79 90.7412 1093.46 90.8292C1093.28 90.8765 1093.18 90.9563 1093.16 91.0722L1092.92 92.0301L1092.81 92.9594L1091.63 91.6979L1088.7 88.7631C1088.44 88.5049 1088.01 88.3895 1087.66 88.4841Z"
              fill="white"
              fillOpacity="0.6"
            />
          </g>
        </g>
        <rect
          fill="black"
          height="906"
          transform="matrix(0.965926 -0.258819 0.707107 0.707107 95.7695 381.353)"
          width="1115"
        />
        <g filter="url(#filter1_ddd_3051_12573)">
          <g clipPath="url(#clip11_3051_12573)">
            <rect
              fill="url(#pattern0_3051_12573)"
              height="702.001"
              transform="matrix(0.965926 -0.258819 0.707107 0.707107 109.154 384.939)"
              width="1099"
            />
          </g>
        </g>
      </g>
      <rect
        fill="url(#gradient)"
        height="615"
        rx="11.5"
        stroke="white"
        strokeOpacity="0.1"
        transform="matrix(0.965926 -0.258819 0.707107 0.707107 58.87 344.865)"
        width="1114"
        x="0.836516"
        y="0.224144"
      />
    </g>
    <defs>
      <linearGradient id="gradient" x1="0%" x2="0%" y1="0%" y2="100%">
        <stop offset="0%" style={{stopColor: "rgba(0, 0, 0, 0)", stopOpacity: 1}} />
        <stop offset="50%" style={{stopColor: "rgba(0, 0, 0, 0.35)", stopOpacity: 1}} />
        <stop offset="100%" style={{stopColor: "#000000", stopOpacity: 1}} />
      </linearGradient>
      <filter
        colorInterpolationFilters="sRGB"
        filterUnits="userSpaceOnUse"
        height="938.161"
        id="filter0_dddddd_3051_12573"
        width="1726.59"
        x="-48"
        y="-1.52588e-05"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        />
        <feOffset dy="1.4113" />
        <feGaussianBlur stdDeviation="1.48048" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0.176471 0 0 0 0 0.188235 0 0 0 0 0.223529 0 0 0 0.0253031 0"
        />
        <feBlend in2="BackgroundImageFix" mode="normal" result="effect1_dropShadow_3051_12573" />
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        />
        <feOffset dy="3.39155" />
        <feGaussianBlur stdDeviation="3.5578" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0.176471 0 0 0 0 0.188235 0 0 0 0 0.223529 0 0 0 0.0363503 0"
        />
        <feBlend
          in2="effect1_dropShadow_3051_12573"
          mode="normal"
          result="effect2_dropShadow_3051_12573"
        />
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        />
        <feOffset dy="6.38599" />
        <feGaussianBlur stdDeviation="6.69903" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0.176471 0 0 0 0 0.188235 0 0 0 0 0.223529 0 0 0 0.045 0"
        />
        <feBlend
          in2="effect2_dropShadow_3051_12573"
          mode="normal"
          result="effect3_dropShadow_3051_12573"
        />
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        />
        <feOffset dy="11.3915" />
        <feGaussianBlur stdDeviation="11.9499" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0.176471 0 0 0 0 0.188235 0 0 0 0 0.223529 0 0 0 0.0536497 0"
        />
        <feBlend
          in2="effect3_dropShadow_3051_12573"
          mode="normal"
          result="effect4_dropShadow_3051_12573"
        />
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        />
        <feOffset dy="21.3066" />
        <feGaussianBlur stdDeviation="22.351" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0.176471 0 0 0 0 0.188235 0 0 0 0 0.223529 0 0 0 0.0646969 0"
        />
        <feBlend
          in2="effect4_dropShadow_3051_12573"
          mode="normal"
          result="effect5_dropShadow_3051_12573"
        />
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        />
        <feOffset dy="51" />
        <feGaussianBlur stdDeviation="53.5" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0.176471 0 0 0 0 0.188235 0 0 0 0 0.223529 0 0 0 0.09 0"
        />
        <feBlend
          in2="effect5_dropShadow_3051_12573"
          mode="normal"
          result="effect6_dropShadow_3051_12573"
        />
        <feBlend
          in="SourceGraphic"
          in2="effect6_dropShadow_3051_12573"
          mode="normal"
          result="shape"
        />
      </filter>
      <filter
        colorInterpolationFilters="sRGB"
        filterUnits="userSpaceOnUse"
        height="925.767"
        id="filter1_ddd_3051_12573"
        width="1702.88"
        x="103.154"
        y="94.497"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        />
        <feOffset dy="2" />
        <feGaussianBlur stdDeviation="2" />
        <feComposite in2="hardAlpha" operator="out" />
        <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0" />
        <feBlend in2="BackgroundImageFix" mode="normal" result="effect1_dropShadow_3051_12573" />
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        />
        <feOffset dy="1" />
        <feGaussianBlur stdDeviation="0.5" />
        <feComposite in2="hardAlpha" operator="out" />
        <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.06 0" />
        <feBlend
          in2="effect1_dropShadow_3051_12573"
          mode="normal"
          result="effect2_dropShadow_3051_12573"
        />
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        />
        <feOffset />
        <feGaussianBlur stdDeviation="3" />
        <feComposite in2="hardAlpha" operator="out" />
        <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0" />
        <feBlend
          in2="effect2_dropShadow_3051_12573"
          mode="normal"
          result="effect3_dropShadow_3051_12573"
        />
        <feBlend
          in="SourceGraphic"
          in2="effect3_dropShadow_3051_12573"
          mode="normal"
          result="shape"
        />
      </filter>
      <pattern
        height="1"
        id="pattern0_3051_12573"
        patternContentUnits="objectBoundingBox"
        width="1"
      >
        <use transform="scale(0.000251383 0.000393546)" xlinkHref="#image0_3051_12573" />
      </pattern>
      <linearGradient
        gradientUnits="userSpaceOnUse"
        id="paint0_linear_3051_12573"
        x1="0"
        x2="1118.48"
        y1="0"
        y2="-5.14145e-05"
      >
        <stop stopColor="#242424" />
        <stop offset="1" />
      </linearGradient>
      <clipPath id="clip0_3051_12573">
        <rect
          fill="white"
          height="616"
          rx="12"
          transform="matrix(0.965926 -0.258819 0.707107 0.707107 59 344.583)"
          width="1115"
        />
      </clipPath>
      <clipPath id="clip1_3051_12573">
        <rect
          fill="white"
          height="52"
          transform="matrix(0.965926 -0.258819 0.707107 0.707107 59 344.583)"
          width="1115"
        />
      </clipPath>
      <clipPath id="clip2_3051_12573">
        <rect
          fill="white"
          height="32"
          rx="8"
          transform="matrix(0.965926 -0.258819 0.707107 0.707107 483.351 239.844)"
          width="250"
        />
      </clipPath>
      <clipPath id="clip3_3051_12573">
        <rect
          fill="white"
          height="14"
          transform="matrix(0.965926 -0.258819 0.707107 0.707107 497.442 244.138)"
          width="14"
        />
      </clipPath>
      <clipPath id="clip4_3051_12573">
        <rect
          fill="white"
          height="14"
          transform="matrix(0.965926 -0.258819 0.707107 0.707107 709.946 187.198)"
          width="14"
        />
      </clipPath>
      <clipPath id="clip5_3051_12573">
        <rect
          fill="white"
          height="20"
          transform="matrix(0.965926 -0.258819 0.707107 0.707107 159.179 332.086)"
          width="20"
        />
      </clipPath>
      <clipPath id="clip6_3051_12573">
        <rect
          fill="white"
          height="20"
          transform="matrix(0.965926 -0.258819 0.707107 0.707107 209.407 318.627)"
          width="20"
        />
      </clipPath>
      <clipPath id="clip7_3051_12573">
        <rect
          fill="white"
          height="20"
          transform="matrix(0.965926 -0.258819 0.707107 0.707107 244.18 309.31)"
          width="20"
        />
      </clipPath>
      <clipPath id="clip8_3051_12573">
        <rect
          fill="white"
          height="20"
          transform="matrix(0.965926 -0.258819 0.707107 0.707107 278.954 299.992)"
          width="20"
        />
      </clipPath>
      <clipPath id="clip9_3051_12573">
        <rect
          fill="white"
          height="20"
          transform="matrix(0.965926 -0.258819 0.707107 0.707107 1108.68 77.6665)"
          width="20"
        />
      </clipPath>
      <clipPath id="clip10_3051_12573">
        <rect
          fill="white"
          height="20"
          transform="matrix(0.965926 -0.258819 0.707107 0.707107 1073.91 86.9839)"
          width="20"
        />
      </clipPath>
      <clipPath id="clip11_3051_12573">
        <rect
          fill="white"
          height="890"
          rx="6"
          transform="matrix(0.965926 -0.258819 0.707107 0.707107 109.154 384.939)"
          width="1099"
        />
      </clipPath>
      <image
        height="2500"
        id="image0_3051_12573"
        width="3978"
        xlinkHref="https://nextuipro.nyc3.cdn.digitaloceanspaces.com/components-images/marketing/hero-section-with-bottom-app-screenshot.png"
      />
    </defs>
  </svg>
);

export default AppScreenshotSkewed;
