"use client";

import React, { useRef, useState } from "react";
import { <PERSON><PERSON>, <PERSON>, CardBody, Chip } from "@heroui/react";
import { Icon } from "@iconify/react";
import { motion as m, AnimatePresence } from "framer-motion";

interface FileUploadProps {
  label: string;
  description?: string;
  files: File[];
  onChange: (files: File[]) => void;
  accept?: string;
  multiple?: boolean;
  maxSize?: number; // in MB
  maxFiles?: number;
  isRequired?: boolean;
  isInvalid?: boolean;
  errorMessage?: string;
}

export default function FileUpload({
  label,
  description,
  files,
  onChange,
  accept = ".pdf,.jpg,.jpeg,.png",
  multiple = true,
  maxSize = 10, // 10MB default
  maxFiles = 5,
  isRequired = false,
  isInvalid = false,
  errorMessage
}: FileUploadProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isDragOver, setIsDragOver] = useState(false);
  const [uploadError, setUploadError] = useState<string>("");

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileIcon = (fileName: string): string => {
    const extension = fileName.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'pdf':
        return 'solar:file-text-bold';
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
        return 'solar:gallery-bold';
      case 'doc':
      case 'docx':
        return 'solar:document-bold';
      default:
        return 'solar:file-bold';
    }
  };

  const validateFiles = (fileList: FileList): File[] => {
    const validFiles: File[] = [];
    const errors: string[] = [];

    Array.from(fileList).forEach((file) => {
      // Check file size
      if (file.size > maxSize * 1024 * 1024) {
        errors.push(`${file.name} is too large (max ${maxSize}MB)`);
        return;
      }

      // Check file type
      const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
      if (accept && !accept.split(',').some(type => type.trim() === fileExtension)) {
        errors.push(`${file.name} is not a supported file type`);
        return;
      }

      validFiles.push(file);
    });

    // Check total file count
    const totalFiles = files.length + validFiles.length;
    if (totalFiles > maxFiles) {
      errors.push(`Maximum ${maxFiles} files allowed`);
      return [];
    }

    if (errors.length > 0) {
      setUploadError(errors.join(', '));
      setTimeout(() => setUploadError(''), 5000);
      return [];
    }

    setUploadError('');
    return validFiles;
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const fileList = e.target.files;
    if (!fileList) return;

    const validFiles = validateFiles(fileList);
    if (validFiles.length > 0) {
      const newFiles = multiple ? [...files, ...validFiles] : validFiles;
      onChange(newFiles);
    }

    // Reset input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);

    const fileList = e.dataTransfer.files;
    if (!fileList) return;

    const validFiles = validateFiles(fileList);
    if (validFiles.length > 0) {
      const newFiles = multiple ? [...files, ...validFiles] : validFiles;
      onChange(newFiles);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const removeFile = (index: number) => {
    const newFiles = files.filter((_, i) => i !== index);
    onChange(newFiles);
  };

  const openFileDialog = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className="w-full space-y-3">
      {/* Label */}
      <div className="flex items-center gap-2">
        <label className="text-sm font-medium text-gray-700 dark:text-slate-300">
          {label}
          {isRequired && <span className="text-red-500 ml-1">*</span>}
        </label>
      </div>

      {/* Description */}
      {description && (
        <p className="text-xs text-gray-500 dark:text-slate-400">
          {description}
        </p>
      )}

      {/* Upload Area */}
      <Card
        className={`border-2 border-dashed transition-all duration-300 cursor-pointer ${
          isDragOver
            ? 'border-cyan-400 bg-cyan-50/50 dark:bg-cyan-900/20'
            : isInvalid
            ? 'border-red-300 dark:border-red-600'
            : 'border-gray-300 dark:border-slate-600 hover:border-cyan-400 dark:hover:border-cyan-400'
        } ${
          isInvalid ? 'bg-red-50/50 dark:bg-red-900/20' : 'bg-gray-50/50 dark:bg-slate-800/50'
        }`}
        isPressable
        onPress={openFileDialog}
      >
        <CardBody
          className="p-6 text-center"
          onDrop={handleDrop}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
        >
          <div className="flex flex-col items-center gap-3">
            <div className={`p-3 rounded-full ${
              isDragOver 
                ? 'bg-cyan-100 dark:bg-cyan-900/30' 
                : 'bg-gray-100 dark:bg-slate-700'
            }`}>
              <Icon 
                icon="solar:cloud-upload-bold" 
                className={`text-2xl ${
                  isDragOver 
                    ? 'text-cyan-500' 
                    : 'text-gray-400 dark:text-slate-500'
                }`} 
              />
            </div>
            
            <div>
              <p className="text-sm font-medium text-gray-700 dark:text-slate-300 mb-1">
                Click to upload or drag and drop
              </p>
              <p className="text-xs text-gray-500 dark:text-slate-400">
                {accept.replace(/\./g, '').toUpperCase()} up to {maxSize}MB each
              </p>
            </div>

            <Button
              size="sm"
              variant="flat"
              color="primary"
              startContent={<Icon icon="solar:add-circle-bold" />}
              className="bg-cyan-100 dark:bg-cyan-900/30 text-cyan-700 dark:text-cyan-300"
            >
              Choose Files
            </Button>
          </div>
        </CardBody>
      </Card>

      {/* Hidden File Input */}
      <input
        ref={fileInputRef}
        type="file"
        accept={accept}
        multiple={multiple}
        onChange={handleFileSelect}
        className="hidden"
      />

      {/* Error Messages */}
      {(uploadError || errorMessage) && (
        <div className="text-xs text-red-500 dark:text-red-400">
          {uploadError || errorMessage}
        </div>
      )}

      {/* File List */}
      <AnimatePresence>
        {files.length > 0 && (
          <m.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="space-y-2"
          >
            <div className="flex items-center justify-between">
              <p className="text-sm font-medium text-gray-700 dark:text-slate-300">
                Uploaded Files ({files.length})
              </p>
              <Chip
                size="sm"
                variant="flat"
                color="success"
                startContent={<Icon icon="solar:check-circle-bold" className="text-xs" />}
              >
                {files.length} file{files.length !== 1 ? 's' : ''}
              </Chip>
            </div>

            <div className="space-y-2 max-h-40 overflow-y-auto">
              {files.map((file, index) => (
                <m.div
                  key={`${file.name}-${index}`}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                  className="flex items-center gap-3 p-3 bg-white/50 dark:bg-slate-700/50 rounded-lg border border-gray-200 dark:border-slate-600"
                >
                  <Icon 
                    icon={getFileIcon(file.name)} 
                    className="text-lg text-gray-500 dark:text-slate-400 flex-shrink-0" 
                  />
                  
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-700 dark:text-slate-300 truncate">
                      {file.name}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-slate-400">
                      {formatFileSize(file.size)}
                    </p>
                  </div>

                  <Button
                    isIconOnly
                    size="sm"
                    variant="light"
                    color="danger"
                    onPress={() => removeFile(index)}
                    className="flex-shrink-0"
                  >
                    <Icon icon="solar:trash-bin-minimalistic-bold" className="text-sm" />
                  </Button>
                </m.div>
              ))}
            </div>
          </m.div>
        )}
      </AnimatePresence>
    </div>
  );
}
