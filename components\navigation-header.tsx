"use client";

import React from "react";
import {
  Navbar,
  Nav<PERSON><PERSON><PERSON>,
  NavbarContent,
  NavbarItem,
  NavbarMenu,
  NavbarMenuItem,
  NavbarMenuToggle,
  Link,
  Button,
  Dropdown,
  DropdownTrigger,
  DropdownMenu,
  DropdownItem,
  Input,
  Avatar,
  Badge,
} from "@heroui/react";
import {Icon} from "@iconify/react";
import { ThemeSwitch } from "./theme-switch";

// import {AcmeIcon} from "./acme";


export default function NavigationHeader() {
  return (
    <div className="w-full">
      <Navbar
        classNames={{
          base: "pt-2 lg:pt-4 lg:bg-transparent lg:backdrop-filter-none",
          wrapper: "px-4 sm:px-6",
          item: "data-[active=true]:text-primary",
        }}
        height="60px"
      >
        <NavbarBrand>
          <NavbarMenuToggle className="mr-2 h-6 sm:hidden" />
          {/* <AcmeIcon /> */}
          <p className="font-bold text-inherit">MONEY
            <span className="text-primary"> FAST</span> FUNDING
          </p>
        </NavbarBrand>
        <NavbarContent
          className="ml-4 hidden h-12 w-full max-w-fit gap-4 rounded-full bg-content2 px-4 dark:bg-content1 sm:flex"
          justify="start"
        >
          <NavbarItem>
            <Link className="flex gap-2 text-inherit" href="#">
              HOME
            </Link>
          </NavbarItem>
          <NavbarItem isActive>
            <Link aria-current="page" className="flex gap-2 text-inherit" href="#">
              ABOUT US
            </Link>
          </NavbarItem>
          <NavbarItem>
            <Link className="flex gap-2 text-inherit" href="#">
              MFF LOANS
            </Link>
          </NavbarItem>
          <NavbarItem>
            <Link className="flex gap-2 text-inherit" href="#">
              APPLY NOW
            </Link>
          </NavbarItem>
          <NavbarItem>
            <Link className="flex gap-2 text-inherit" href="#">
              CONTACT US
            </Link>
          </NavbarItem>
          <NavbarItem>
            <Link className="flex gap-2 text-inherit" href="#">
              UNDERWRITING DEPT
            </Link>
          </NavbarItem>
        </NavbarContent>
        <NavbarContent
          className="ml-auto flex h-12 max-w-fit items-center gap-0 rounded-full p-0 lg:bg-content2 lg:px-1 lg:dark:bg-content1"
          justify="end"
        >

          {/* Mobile search */}
          <NavbarItem className="lg:hidden">
            <Button isIconOnly radius="full" variant="light">
              <Icon className="text-default-500" icon="solar:magnifer-linear" width={20} />
            </Button>
          </NavbarItem>
          {/* Theme change */}
          <NavbarItem className="hidden lg:flex">
          <ThemeSwitch />
          </NavbarItem>
          {/* Settings */}
          <NavbarItem className="hidden lg:flex">
            <Button isIconOnly radius="full" variant="light">
              <Icon className="text-default-500" icon="solar:settings-linear" width={24} />
            </Button>
          </NavbarItem>
        </NavbarContent>

        {/* Mobile Menu */}
        <NavbarMenu>
          <NavbarMenuItem>
            <Link className="w-full" color="foreground" href="#">
              HOME
            </Link>
          </NavbarMenuItem>
          <NavbarMenuItem isActive>
            <Link aria-current="page" className="w-full" color="primary" href="#">
              ABOUT US
            </Link>
          </NavbarMenuItem>
          <NavbarMenuItem>
            <Link className="w-full" color="foreground" href="#">
              MFF LOANS
            </Link>
          </NavbarMenuItem>
          <NavbarMenuItem>
            <Link className="w-full" color="foreground" href="#">
              APPLY NOW
            </Link>
          </NavbarMenuItem>
          <NavbarMenuItem>
            <Link className="w-full" color="foreground" href="#">
              CONTACT US
            </Link>
          </NavbarMenuItem>
          <NavbarMenuItem>
            <Link className="w-full" color="foreground" href="#">
              UNDERWRITING DEPT
            </Link>
          </NavbarMenuItem>
        </NavbarMenu>
      </Navbar>
    </div>
  );
}
