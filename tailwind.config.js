import {heroui} from "@heroui/theme"

/** @type {import('tailwindcss').Config} */
const config = {
  content: [
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
    "./node_modules/@heroui/theme/dist/**/*.{js,ts,jsx,tsx}"
  ],
  theme: {
    extend: {
      fontFamily: {
        sans: ["var(--font-sans)"],
        mono: ["var(--font-mono)"],
      },
      colors: {
        // Glassmorphism colors for dark theme
        'glass-dark': {
          'bg': 'rgba(15, 23, 42, 0.8)',
          'border': 'rgba(59, 130, 246, 0.3)',
          'accent': '#0ea5e9',
          'green': '#10b981',
          'purple': '#8b5cf6',
        },
        // Glassmorphism colors for light theme
        'glass-light': {
          'bg': 'rgba(255, 255, 255, 0.8)',
          'border': 'rgba(59, 130, 246, 0.2)',
          'accent': '#0ea5e9',
          'green': '#10b981',
          'purple': '#8b5cf6',
        },
        // Financial brand colors
        'finance': {
          'primary': '#0ea5e9',
          'secondary': '#10b981',
          'accent': '#8b5cf6',
          'dark': '#1e293b',
          'light': '#f8fafc',
        }
      },
      backgroundImage: {
        "hero-section-title":
          "linear-gradient(91deg, #FFF 32.88%, rgba(255, 255, 255, 0.40) 99.12%)",
        "hero-section-title-light":
          "linear-gradient(91deg, #000 32.88%, rgba(0, 0, 0, 0.40) 99.12%)",
        "finance-gradient-dark":
          "linear-gradient(135deg, #0ea5e9 0%, #10b981 50%, #8b5cf6 100%)",
        "finance-gradient-light":
          "linear-gradient(135deg, #0ea5e9 0%, #10b981 50%, #8b5cf6 100%)",
        "glass-gradient-dark":
          "linear-gradient(135deg, rgba(14, 165, 233, 0.1) 0%, rgba(16, 185, 129, 0.1) 50%, rgba(139, 92, 246, 0.1) 100%)",
        "glass-gradient-light":
          "linear-gradient(135deg, rgba(14, 165, 233, 0.05) 0%, rgba(16, 185, 129, 0.05) 50%, rgba(139, 92, 246, 0.05) 100%)",
      },
      backdropBlur: {
        'xs': '2px',
        'glass': '16px',
      },
      animation: {
        'float': 'float 6s ease-in-out infinite',
        'float-delayed': 'float 6s ease-in-out infinite 2s',
        'pulse-glow': 'pulse-glow 2s ease-in-out infinite alternate',
      },
      keyframes: {
        float: {
          '0%, 100%': { transform: 'translateY(0px)' },
          '50%': { transform: 'translateY(-20px)' },
        },
        'pulse-glow': {
          '0%': { boxShadow: '0 0 20px rgba(14, 165, 233, 0.3)' },
          '100%': { boxShadow: '0 0 40px rgba(14, 165, 233, 0.6)' },
        },
      },
    },
  },
  darkMode: "class",
  plugins: [heroui()],
}

module.exports = config;