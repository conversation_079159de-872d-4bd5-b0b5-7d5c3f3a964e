'use client';

import React from "react";
import {Button} from "@heroui/react";
import {Icon} from "@iconify/react";
import {AnimatePresence, LazyMotion, domAnimation, m} from "framer-motion";

// Floating Stats Card Component
const FloatingStatsCard = ({ 
  title, 
  value, 
  icon, 
  delay = 0,
  className = "" 
}: {
  title: string;
  value: string;
  icon: string;
  delay?: number;
  className?: string;
}) => (
  <m.div
    initial={{ opacity: 0, y: 50, scale: 0.8 }}
    animate={{ opacity: 1, y: 0, scale: 1 }}
    transition={{ 
      duration: 0.8, 
      delay: delay,
      type: "spring",
      bounce: 0.3
    }}
    className={`backdrop-blur-glass bg-white/20 dark:bg-white/10 border border-white/30 dark:border-white/20 rounded-2xl p-6 shadow-2xl hover:shadow-finance-primary/20 transition-all duration-300 hover:scale-105 animate-float ${className}`}
  >
    <div className="flex items-center gap-3 mb-2">
      <div className="p-2 rounded-lg bg-finance-gradient-dark">
        <Icon icon={icon} className="w-5 h-5 text-white" />
      </div>
    </div>
    <div className="text-2xl font-bold bg-finance-gradient-dark bg-clip-text text-transparent">
      {value}
    </div>
    <div className="text-sm text-gray-600 dark:text-gray-300">
      {title}
    </div>
  </m.div>
);

// Main Hero Component
export default function HeroGlassmorphism() {
  const statsData = [
    { title: "Approval Time", value: "24hrs", icon: "solar:clock-circle-bold" },
    { title: "Funding Range", value: "$50K-$5M", icon: "solar:dollar-minimalistic-bold" },
    { title: "Success Rate", value: "98%", icon: "solar:chart-2-bold" },
    { title: "Happy Clients", value: "500+", icon: "solar:users-group-rounded-bold" },
  ];

  return (
    <div className="relative flex h-screen min-h-dvh w-full flex-col overflow-hidden">
      {/* Video Background */}
      <div className="absolute inset-0 z-0">
        <video
          autoPlay
          loop
          muted
          playsInline
          className="w-full h-full object-cover"
        >
          <source src="/video.mp4" type="video/mp4" />
        </video>
        {/* Video Overlay */}
        <div className="absolute inset-0 bg-black/30 dark:bg-black/60" />
        <div className="absolute inset-0 bg-glass-gradient-light dark:bg-glass-gradient-dark" />
      </div>

      {/* Main Content */}
      <main className="relative z-10 container mx-auto mt-[80px] flex max-w-[1200px] flex-col items-start px-8">
        <section className="z-20 flex flex-col items-start justify-center gap-[18px] sm:gap-6">
          
          {/* Announcement Badge */}
          <LazyMotion features={domAnimation}>
            <m.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <Button
                className="h-9 overflow-hidden backdrop-blur-md bg-white/20 dark:bg-white/10 border-1 border-white/30 dark:border-white/20 px-[18px] py-2 text-small font-normal leading-5 text-gray-700 dark:text-white/80 hover:bg-white/30 dark:hover:bg-white/20 transition-all duration-300"
                endContent={
                  <Icon
                    className="flex-none outline-none [&>path]:stroke-[2] text-finance-accent"
                    icon="solar:arrow-right-linear"
                    width={20}
                  />
                }
                radius="full"
                variant="bordered"
              >
                Fast Funding Available Now
              </Button>
            </m.div>

            {/* Main Hero Content */}
            <div className="flex flex-col lg:flex-row items-start gap-12 w-full">
              
              {/* Left Side - Main Content */}
              <div className="flex-1 max-w-2xl">
                {/* Main Glassmorphism Card */}
                <m.div
                  initial={{ opacity: 0, x: -50 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.8, delay: 0.4 }}
                  className="backdrop-blur-glass bg-white/30 dark:bg-white/10 border border-white/40 dark:border-white/20 rounded-3xl p-8 lg:p-12 shadow-2xl"
                >
                  {/* Main Title */}
                  <m.div
                    initial={{ opacity: 0, y: 30 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8, delay: 0.6 }}
                    className="text-start text-[clamp(40px,8vw,64px)] font-bold leading-[1.1] tracking-tight mb-6"
                  >
                    <div className="bg-finance-gradient-dark bg-clip-text text-transparent">
                      Fast Business
                      <br />
                      Funding Solutions
                    </div>
                  </m.div>

                  {/* Description */}
                  <m.div
                    initial={{ opacity: 0, y: 30 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8, delay: 0.8 }}
                    className="text-start font-normal leading-7 text-gray-700 dark:text-white/80 text-lg mb-8 max-w-lg"
                  >
                    Get approved in 24 hours with funding from $50K to $5M. 
                    Simple application, fast approval, and competitive rates for your business growth.
                  </m.div>

                  {/* Action Buttons */}
                  <m.div
                    initial={{ opacity: 0, y: 30 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8, delay: 1.0 }}
                    className="flex flex-col sm:flex-row gap-4"
                  >
                    <Button
                      className="h-12 px-8 bg-finance-gradient-dark text-white font-semibold shadow-lg hover:shadow-finance-primary/30 transition-all duration-300 hover:scale-105"
                      radius="full"
                      size="lg"
                    >
                      Apply Now
                    </Button>
                    <Button
                      className="h-12 px-8 backdrop-blur-md bg-white/20 dark:bg-white/10 border-1 border-white/40 dark:border-white/30 text-gray-700 dark:text-white font-semibold hover:bg-white/30 dark:hover:bg-white/20 transition-all duration-300"
                      radius="full"
                      size="lg"
                      variant="bordered"
                    >
                      Learn More
                    </Button>
                  </m.div>
                </m.div>
              </div>

              {/* Right Side - Floating Stats Cards */}
              <div className="flex-1 relative min-h-[400px] lg:min-h-[500px]">
                <div className="absolute inset-0 grid grid-cols-2 gap-4 lg:gap-6">
                  {statsData.map((stat, index) => (
                    <FloatingStatsCard
                      key={stat.title}
                      title={stat.title}
                      value={stat.value}
                      icon={stat.icon}
                      delay={1.2 + index * 0.2}
                      className={index % 2 === 0 ? "animate-float" : "animate-float-delayed"}
                    />
                  ))}
                </div>
              </div>
            </div>
          </LazyMotion>
        </section>
      </main>

      {/* Floating Background Elements */}
      <div className="absolute inset-0 pointer-events-none overflow-hidden">
        <div className="absolute top-1/4 left-1/4 w-32 h-32 bg-finance-primary/10 rounded-full blur-xl animate-pulse" />
        <div className="absolute top-3/4 right-1/4 w-24 h-24 bg-finance-secondary/10 rounded-full blur-xl animate-pulse" style={{ animationDelay: '2s' }} />
        <div className="absolute top-1/2 right-1/3 w-20 h-20 bg-finance-accent/10 rounded-full blur-xl animate-pulse" style={{ animationDelay: '4s' }} />
      </div>
    </div>
  );
}
