'use client';

import React from "react";
import {Button} from "@heroui/react";
import {Icon} from "@iconify/react";
import {AnimatePresence, LazyMotion, domAnimation, m} from "framer-motion";

// Compact Stats Card Component (matching reference design)
const StatsCard = ({
  title,
  value,
  icon,
  delay = 0,
  accentColor = "cyan"
}: {
  title: string;
  value: string;
  icon: string;
  delay?: number;
  accentColor?: string;
}) => {
  const colorClasses = {
    cyan: "bg-cyan-500",
    green: "bg-emerald-500",
    blue: "bg-blue-500",
    purple: "bg-purple-500"
  };

  return (
    <m.div
      initial={{ opacity: 0, y: 30, scale: 0.9 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      transition={{
        duration: 0.6,
        delay: delay,
        type: "spring",
        bounce: 0.2
      }}
      className="backdrop-blur-md bg-slate-800/60 border border-slate-700/50 rounded-2xl p-4 shadow-xl hover:shadow-cyan-500/20 transition-all duration-300 hover:scale-105 group"
    >
      {/* Horizontal Layout: Icon + Value */}
      <div className="flex items-center gap-3 mb-2">
        <div className={`p-2 rounded-lg ${colorClasses[accentColor as keyof typeof colorClasses]} shadow-lg flex-shrink-0`}>
          <Icon icon={icon} className="w-4 h-4 text-white" />
        </div>
        <div className="text-2xl font-bold text-white">
          {value}
        </div>
      </div>
      {/* Title Below */}
      <div className="text-sm text-slate-300 leading-tight">
        {title}
      </div>
    </m.div>
  );
};

// Main Hero Component
export default function HeroGlassmorphism() {
  const statsData = [
    { title: "Approval Time", value: "24h", icon: "solar:clock-circle-bold", accentColor: "cyan" },
    { title: "Funding Range", value: "$5K", icon: "solar:dollar-minimalistic-bold", accentColor: "green" },
    { title: "Success Rate", value: "98%", icon: "solar:chart-2-bold", accentColor: "blue" },
    { title: "Happy Clients", value: "500+", icon: "solar:users-group-rounded-bold", accentColor: "purple" },
  ];

  return (
    <div className="relative flex min-h-screen lg:h-screen w-full flex-col overflow-hidden bg-slate-900">
      {/* Video Background */}
      <div className="absolute inset-0 z-0">
        <video
          autoPlay
          loop
          muted
          playsInline
          className="w-full h-full object-cover opacity-40"
        >
          <source src="/video.mp4" type="video/mp4" />
        </video>
        {/* Dark overlay for better contrast */}
        <div className="absolute inset-0 bg-slate-900/80" />
        {/* Subtle gradient overlay */}
        <div className="absolute inset-0 bg-gradient-to-br from-slate-900/50 via-transparent to-cyan-900/20" />
      </div>

      {/* Main Content - Mobile Scrollable, Desktop Full Height */}
      <main className="relative z-10 pt-20 sm:pt-24 pb-12 lg:pb-8 flex-1 lg:min-h-screen">
        <div className="container mx-auto max-w-[1400px] px-4 sm:px-8">
          <LazyMotion features={domAnimation}>
            {/* Announcement Badge */}
            <m.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="mb-6 sm:mb-8 text-center lg:text-left"
            >
              <div className="inline-flex items-center gap-2 backdrop-blur-md bg-slate-800/60 border border-cyan-500/30 px-4 py-2 rounded-full text-sm text-cyan-300 shadow-lg">
                <div className="w-2 h-2 bg-cyan-400 rounded-full animate-pulse"></div>
                Fast Funding Available Now
                <Icon icon="solar:arrow-right-linear" className="w-4 h-4" />
              </div>
            </m.div>

            {/* Main Hero Content - Equal Width Columns */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 lg:gap-8 w-full items-start">

              {/* Left Side - Main Content Card */}
              <div className="w-full ">
                <m.div
                  initial={{ opacity: 0, x: -50 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.8, delay: 0.4 }}
                  className="backdrop-blur-md bg-slate-800/60 border border-slate-700/50 rounded-3xl p-4 sm:p-6 lg:p-8 shadow-2xl h-full flex flex-col"
                >
                  {/* Main Title - Optimized Size */}
                  <m.div
                    initial={{ opacity: 0, y: 30 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8, delay: 0.6 }}
                    className="mb-6"
                  >
                    <h1 className="text-3xl sm:text-4xl lg:text-5xl xl:text-6xl font-bold leading-tight text-center lg:text-left">
                      <span className="text-cyan-400">Fast</span>
                      <br />
                      <span className="text-white">Business</span>
                      <br />
                      <span className="text-white">Funding</span>
                      <br />
                      <span className="text-cyan-400">Solutions</span>
                    </h1>
                  </m.div>

                  {/* Description */}
                  <m.div
                    initial={{ opacity: 0, y: 30 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8, delay: 0.8 }}
                    className="text-slate-300 text-base sm:text-lg leading-relaxed mb-6 max-w-lg text-center lg:text-left"
                  >
                    Get approved in 24 hours with funding from $50K to $5M. Simple application, fast approval, and competitive rates for your business growth.
                  </m.div>

                  {/* Key Features List */}
                  <m.div
                    initial={{ opacity: 0, y: 30 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8, delay: 1.0 }}
                    className="mb-8"
                  >
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 text-sm">
                      <div className="flex items-center gap-2 text-slate-300">
                        <div className="w-2 h-2 bg-cyan-400 rounded-full"></div>
                        No collateral required
                      </div>
                      <div className="flex items-center gap-2 text-slate-300">
                        <div className="w-2 h-2 bg-emerald-400 rounded-full"></div>
                        Same-day approval
                      </div>
                      <div className="flex items-center gap-2 text-slate-300">
                        <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                        Flexible repayment
                      </div>
                      <div className="flex items-center gap-2 text-slate-300">
                        <div className="w-2 h-2 bg-purple-400 rounded-full"></div>
                        Bad credit OK
                      </div>
                    </div>
                  </m.div>

                  {/* Action Buttons */}
                  <m.div
                    initial={{ opacity: 0, y: 30 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8, delay: 1.2 }}
                    className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start"
                  >
                    <Button
                      className="h-12 px-8 bg-gradient-to-r from-cyan-500 to-cyan-600 text-white font-semibold shadow-lg hover:shadow-cyan-500/30 transition-all duration-300 hover:scale-105"
                      radius="lg"
                      size="lg"
                    >
                      Apply Now
                    </Button>
                    <Button
                      className="h-12 px-8 backdrop-blur-md bg-slate-700/50 border-1 border-slate-600 text-white font-semibold hover:bg-slate-600/50 transition-all duration-300"
                      radius="lg"
                      size="lg"
                      variant="bordered"
                    >
                      Learn More
                    </Button>
                  </m.div>
                </m.div>
              </div>

              {/* Right Side - Stats & Trust Card */}
              <div className="w-full">
                <m.div
                  initial={{ opacity: 0, x: 50 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.8, delay: 0.6 }}
                  className="backdrop-blur-md bg-slate-800/60 border border-slate-700/50 rounded-3xl p-4 sm:p-6 lg:p-8 shadow-2xl h-full flex flex-col"
                >
                  {/* Stats Grid */}
                  <div className="grid grid-cols-2 gap-4 mb-6">
                    {statsData.map((stat, index) => (
                      <StatsCard
                        key={stat.title}
                        title={stat.title}
                        value={stat.value}
                        icon={stat.icon}
                        accentColor={stat.accentColor}
                        delay={1.4 + index * 0.1}
                      />
                    ))}
                  </div>

                  {/* Enhanced Trust & Benefits Section */}
                  <div className="flex-1 flex flex-col">
                    <div className="text-center mb-4">
                      <div className="text-sm font-semibold text-cyan-400 mb-1">Why Choose Money Fast Funding?</div>
                      <div className="text-xs text-slate-400">Trusted by 10,000+ businesses nationwide</div>
                    </div>

                    {/* Trust Badges */}
                    <div className="grid grid-cols-3 gap-3 mb-4">
                      <div className="text-center">
                        <div className="w-8 h-8 bg-emerald-500/20 rounded-lg flex items-center justify-center mx-auto mb-1">
                          <Icon icon="solar:shield-check-bold" className="w-4 h-4 text-emerald-400" />
                        </div>
                        <div className="text-xs text-slate-300 font-medium">Bank-Level Security</div>
                      </div>
                      <div className="text-center">
                        <div className="w-8 h-8 bg-blue-500/20 rounded-lg flex items-center justify-center mx-auto mb-1">
                          <Icon icon="solar:verified-check-bold" className="w-4 h-4 text-blue-400" />
                        </div>
                        <div className="text-xs text-slate-300 font-medium">Licensed Lender</div>
                      </div>
                      <div className="text-center">
                        <div className="w-8 h-8 bg-yellow-500/20 rounded-lg flex items-center justify-center mx-auto mb-1">
                          <Icon icon="solar:star-bold" className="w-4 h-4 text-yellow-400" />
                        </div>
                        <div className="text-xs text-slate-300 font-medium">5-Star Rated</div>
                      </div>
                    </div>

                    {/* Quick Benefits */}
                    <div className="space-y-2 grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
                      <div className="flex items-center gap-2 text-xs text-slate-300">
                        <div className="w-1.5 h-1.5 bg-cyan-400 rounded-full"></div>
                        <span>No personal guarantees required</span>
                      </div>
                      <div className="flex items-center gap-2 text-xs text-slate-300">
                        <div className="w-1.5 h-1.5 bg-emerald-400 rounded-full"></div>
                        <span>Funds in your account within 24 hours</span>
                      </div>
                      <div className="flex items-center gap-2 text-xs text-slate-300">
                        <div className="w-1.5 h-1.5 bg-blue-400 rounded-full"></div>
                        <span>Use funds for any business purpose</span>
                      </div>
                      <div className="flex items-center gap-2 text-xs text-slate-300">
                        <div className="w-1.5 h-1.5 bg-purple-400 rounded-full"></div>
                        <span>No hidden fees or prepayment penalties</span>
                      </div>
                    </div>

                    {/* Quick Contact - At Bottom */}
                    <div className="border-t border-slate-700/50 pt-4 mt-auto">
                      <div className="text-center">
                        <div className="text-xs text-slate-400 mb-2">Need help? Call us now</div>
                        <div className="text-sm font-semibold text-cyan-400">1-800-FAST-FUND</div>
                        <div className="text-xs text-slate-500">Available 24/7</div>
                      </div>
                    </div>
                  </div>
                </m.div>
              </div>
            </div>
          </LazyMotion>
        </div>
      </main>

      {/* Subtle Background Elements */}
      <div className="absolute inset-0 pointer-events-none overflow-hidden">
        <div className="absolute top-1/4 left-1/4 w-32 h-32 bg-cyan-500/5 rounded-full blur-3xl animate-pulse" />
        <div className="absolute top-3/4 right-1/4 w-24 h-24 bg-emerald-500/5 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '2s' }} />
        <div className="absolute top-1/2 right-1/3 w-20 h-20 bg-blue-500/5 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '4s' }} />
      </div>
    </div>
  );
}
