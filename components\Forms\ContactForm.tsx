"use client";

import React, { useState } from "react";
import { 
  <PERSON>, 
  Input, 
  <PERSON><PERSON>, 
  <PERSON>, 
  Card<PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>,
  Divider
} from "@heroui/react";
import { Icon } from "@iconify/react";
import { motion as m } from "framer-motion";
import FileUpload from "./FileUpload";
import CurrencyInput from "./CurrencyInput";

interface FormData {
  name: string;
  email: string;
  phone: string;
  amount: string;
  bankStatements: File[];
  additionalFiles: File[];
}

interface FormErrors {
  name?: string;
  email?: string;
  phone?: string;
  amount?: string;
  bankStatements?: string;
}

export default function ContactForm() {
  const [formData, setFormData] = useState<FormData>({
    name: "",
    email: "",
    phone: "",
    amount: "",
    bankStatements: [],
    additionalFiles: []
  });

  const [errors, setErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    // Name validation
    if (!formData.name.trim()) {
      newErrors.name = "Name is required";
    } else if (formData.name.trim().length < 2) {
      newErrors.name = "Name must be at least 2 characters";
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!formData.email.trim()) {
      newErrors.email = "Email is required";
    } else if (!emailRegex.test(formData.email)) {
      newErrors.email = "Please enter a valid email address";
    }

    // Phone validation
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    if (!formData.phone.trim()) {
      newErrors.phone = "Phone number is required";
    } else if (!phoneRegex.test(formData.phone.replace(/[\s\-\(\)]/g, ""))) {
      newErrors.phone = "Please enter a valid phone number";
    }

    // Amount validation
    if (!formData.amount.trim()) {
      newErrors.amount = "Funding amount is required";
    } else {
      const amount = parseFloat(formData.amount.replace(/[,$]/g, ""));
      if (isNaN(amount) || amount < 5000) {
        newErrors.amount = "Minimum funding amount is $5,000";
      } else if (amount > 5000000) {
        newErrors.amount = "Maximum funding amount is $5,000,000";
      }
    }

    // Bank statements validation
    if (formData.bankStatements.length === 0) {
      newErrors.bankStatements = "4 months of bank statements are required";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Handle successful submission
      console.log("Form submitted:", formData);
      
      // Reset form
      setFormData({
        name: "",
        email: "",
        phone: "",
        amount: "",
        bankStatements: [],
        additionalFiles: []
      });
      
      // Show success message (you can implement toast notification here)
      alert("Application submitted successfully! We'll contact you within 1-3 hours.");
      
    } catch (error) {
      console.error("Submission error:", error);
      alert("There was an error submitting your application. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error when user starts typing
    if (errors[field as keyof FormErrors]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const handleFileChange = (field: 'bankStatements' | 'additionalFiles', files: File[]) => {
    setFormData(prev => ({ ...prev, [field]: files }));
    
    // Clear error when files are added
    if (field === 'bankStatements' && errors.bankStatements) {
      setErrors(prev => ({ ...prev, bankStatements: undefined }));
    }
  };

  return (
    <div className="w-full max-w-7xl mx-auto p-4">
      <m.div
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <Card className="backdrop-blur-md bg-white/90 dark:bg-slate-800/90 border border-gray-200/50 dark:border-slate-700/50 shadow-2xl overflow-hidden">
          <CardHeader className="text-center pb-6 bg-gradient-to-r from-cyan-50 to-blue-50 dark:from-slate-800 dark:to-slate-700">
            <div className="w-full">
              <p className="text-sm font-medium text-gray-500 dark:text-slate-400 uppercase tracking-wider mb-3">
                CONTACT US
              </p>
              <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 dark:text-white mb-2">
                Contact Us and Get Your Approval in 1 to 3 Hours
              </h2>
              <p className="text-gray-600 dark:text-slate-300 max-w-2xl mx-auto">
                Fill out our quick application form and get pre-approved for funding up to $5M.
                Our team will contact you within 1-3 hours with your approval decision.
              </p>
            </div>
          </CardHeader>

          <CardBody className="p-8">
            <Form onSubmit={handleSubmit} className="space-y-8">

              {/* Top Section - Personal Information in 2 Columns */}
              <div className="mb-8">
                <div className="flex items-center mb-6">
                  <div className="w-12 h-12 bg-gradient-to-r from-cyan-500 to-blue-500 rounded-full flex items-center justify-center mr-4">
                    <Icon icon="solar:user-bold" className="text-white text-xl" />
                  </div>
                  <div>
                    <h3 className="text-2xl font-bold text-gray-900 dark:text-white">Personal Information</h3>
                    <p className="text-gray-600 dark:text-slate-400">Tell us about yourself and your funding needs</p>
                  </div>
                </div>

                {/* 2-Column Grid for Form Fields */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Name Field */}
                  <m.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.1 }}
                  >
                    <Input
                      label="Full Name"
                      placeholder="Enter your full name"
                      value={formData.name}
                      onChange={(e) => handleInputChange('name', e.target.value)}
                      isRequired
                      isInvalid={!!errors.name}
                      errorMessage={errors.name}
                      size="lg"
                      startContent={
                        <Icon icon="solar:user-bold" className="text-gray-400 dark:text-slate-500" />
                      }
                      classNames={{
                        input: "text-gray-900 dark:text-white text-base",
                        inputWrapper: "bg-white/80 dark:bg-slate-700/80 border-2 border-gray-200 dark:border-slate-600 hover:border-cyan-400 dark:hover:border-cyan-400 focus-within:border-cyan-500 dark:focus-within:border-cyan-500 transition-all duration-300 shadow-sm hover:shadow-md",
                        label: "text-gray-700 dark:text-slate-300 font-medium"
                      }}
                    />
                  </m.div>

                  {/* Email Field */}
                  <m.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.2 }}
                  >
                    <Input
                      type="email"
                      label="Email Address"
                      placeholder="Enter your email address"
                      value={formData.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      isRequired
                      isInvalid={!!errors.email}
                      errorMessage={errors.email}
                      size="lg"
                      startContent={
                        <Icon icon="solar:letter-bold" className="text-gray-400 dark:text-slate-500" />
                      }
                      classNames={{
                        input: "text-gray-900 dark:text-white text-base",
                        inputWrapper: "bg-white/80 dark:bg-slate-700/80 border-2 border-gray-200 dark:border-slate-600 hover:border-cyan-400 dark:hover:border-cyan-400 focus-within:border-cyan-500 dark:focus-within:border-cyan-500 transition-all duration-300 shadow-sm hover:shadow-md",
                        label: "text-gray-700 dark:text-slate-300 font-medium"
                      }}
                    />
                  </m.div>

                  {/* Phone Field */}
                  <m.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.3 }}
                  >
                    <Input
                      type="tel"
                      label="Phone Number"
                      placeholder="Enter your phone number"
                      value={formData.phone}
                      onChange={(e) => handleInputChange('phone', e.target.value)}
                      isRequired
                      isInvalid={!!errors.phone}
                      errorMessage={errors.phone}
                      size="lg"
                      startContent={
                        <Icon icon="solar:phone-bold" className="text-gray-400 dark:text-slate-500" />
                      }
                      classNames={{
                        input: "text-gray-900 dark:text-white text-base",
                        inputWrapper: "bg-white/80 dark:bg-slate-700/80 border-2 border-gray-200 dark:border-slate-600 hover:border-cyan-400 dark:hover:border-cyan-400 focus-within:border-cyan-500 dark:focus-within:border-cyan-500 transition-all duration-300 shadow-sm hover:shadow-md",
                        label: "text-gray-700 dark:text-slate-300 font-medium"
                      }}
                    />
                  </m.div>

                  {/* Amount Field */}
                  <m.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.4 }}
                  >
                    <CurrencyInput
                      label="Funding Amount Needed"
                      placeholder="Enter funding amount"
                      value={formData.amount}
                      onChange={(value) => handleInputChange('amount', value)}
                      isRequired
                      isInvalid={!!errors.amount}
                      errorMessage={errors.amount}
                    />
                  </m.div>
                </div>
              </div>

              {/* Bottom Section - Documents & Submit in 2 Columns */}
              <div className="space-y-8">
                <div className="flex items-center mb-6">
                  <div className="w-12 h-12 bg-gradient-to-r from-emerald-500 to-cyan-500 rounded-full flex items-center justify-center mr-4">
                    <Icon icon="solar:document-bold" className="text-white text-xl" />
                  </div>
                  <div>
                    <h3 className="text-2xl font-bold text-gray-900 dark:text-white">Required Documents</h3>
                    <p className="text-gray-600 dark:text-slate-400">Upload your financial documents securely</p>
                  </div>
                </div>

                {/* Security Notice */}
                <m.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.5 }}
                  className="bg-blue-50 dark:bg-slate-800/50 border border-blue-200 dark:border-slate-600 rounded-xl p-6 mb-6"
                >
                  <div className="flex items-start">
                    <Icon icon="solar:shield-check-bold" className="text-blue-500 mr-3 mt-1 text-xl" />
                    <div>
                      <p className="text-base font-semibold text-blue-800 dark:text-blue-300">Secure Upload</p>
                      <p className="text-sm text-blue-600 dark:text-blue-400">All files are encrypted and stored securely. Your information is protected with bank-level security.</p>
                    </div>
                  </div>
                </m.div>

                {/* 2-Column Grid for File Uploads and Benefits */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">

                  {/* Left Column - File Uploads */}
                  <div className="space-y-6">
                    {/* Bank Statements Upload */}
                    <m.div
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.5, delay: 0.6 }}
                    >
                      <FileUpload
                        label="4 Months Bank Statements"
                        description="Upload your last 4 months of bank statements (PDF, JPG, PNG)"
                        files={formData.bankStatements}
                        onChange={(files) => handleFileChange('bankStatements', files)}
                        accept=".pdf,.jpg,.jpeg,.png"
                        multiple
                        isRequired
                        isInvalid={!!errors.bankStatements}
                        errorMessage={errors.bankStatements}
                      />
                    </m.div>

                    {/* Additional Files Upload */}
                    <m.div
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.5, delay: 0.7 }}
                    >
                      <FileUpload
                        label="Additional Documents"
                        description="Upload any additional documents (Optional)"
                        files={formData.additionalFiles}
                        onChange={(files) => handleFileChange('additionalFiles', files)}
                        accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                        multiple
                      />
                    </m.div>
                  </div>

                  {/* Right Column - Benefits & Submit */}
                  <div className="space-y-6">
                    {/* Benefits Section */}
                    <m.div
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.5, delay: 0.8 }}
                      className="bg-gradient-to-br from-gray-50 to-cyan-50 dark:from-slate-800/80 dark:to-slate-700/80 rounded-xl p-6 border border-gray-200 dark:border-slate-600"
                    >
                      <h4 className="text-xl font-bold text-gray-900 dark:text-white mb-6 flex items-center">
                        <Icon icon="solar:star-bold" className="text-yellow-500 mr-3 text-xl" />
                        Why Choose Money Fast Funding?
                      </h4>
                      <div className="space-y-4">
                        <div className="flex items-center text-base text-gray-700 dark:text-slate-300">
                          <Icon icon="solar:clock-circle-bold" className="text-green-500 mr-3 text-lg" />
                          1-3 Hour Approval Process
                        </div>
                        <div className="flex items-center text-base text-gray-700 dark:text-slate-300">
                          <Icon icon="solar:dollar-minimalistic-bold" className="text-green-500 mr-3 text-lg" />
                          Up to $5M in Funding
                        </div>
                        <div className="flex items-center text-base text-gray-700 dark:text-slate-300">
                          <Icon icon="solar:shield-check-bold" className="text-green-500 mr-3 text-lg" />
                          No Collateral Required
                        </div>
                        <div className="flex items-center text-base text-gray-700 dark:text-slate-300">
                          <Icon icon="solar:chart-2-bold" className="text-green-500 mr-3 text-lg" />
                          Competitive Rates
                        </div>
                      </div>
                    </m.div>

                    {/* Submit Button */}
                    <m.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5, delay: 0.9 }}
                    >
                      <Button
                        type="submit"
                        size="lg"
                        isLoading={isSubmitting}
                        className="w-full h-16 bg-gradient-to-r from-cyan-500 via-blue-500 to-purple-500 hover:from-cyan-600 hover:via-blue-600 hover:to-purple-600 text-white font-bold text-lg shadow-xl hover:shadow-2xl hover:shadow-cyan-500/30 transition-all duration-300 transform hover:scale-[1.02]"
                        startContent={
                          !isSubmitting && <Icon icon="solar:paper-plane-bold" className="text-xl" />
                        }
                      >
                        {isSubmitting ? "Submitting Application..." : "SUBMIT APPLICATION"}
                      </Button>
                      <p className="text-sm text-center text-gray-500 dark:text-slate-400 mt-4">
                        By submitting, you agree to our terms and privacy policy
                      </p>
                    </m.div>
                  </div>
                </div>
              </div>
            </Form>
          </CardBody>
        </Card>
      </m.div>
    </div>
  );
}
