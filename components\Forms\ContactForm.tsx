"use client";

import React, { useState } from "react";
import { 
  <PERSON>, 
  Input, 
  <PERSON><PERSON>, 
  <PERSON>, 
  Card<PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>,
  Divider
} from "@heroui/react";
import { Icon } from "@iconify/react";
import { motion as m } from "framer-motion";
import FileUpload from "./FileUpload";
import CurrencyInput from "./CurrencyInput";

interface FormData {
  name: string;
  email: string;
  phone: string;
  amount: string;
  bankStatements: File[];
  additionalFiles: File[];
}

interface FormErrors {
  name?: string;
  email?: string;
  phone?: string;
  amount?: string;
  bankStatements?: string;
}

export default function ContactForm() {
  const [formData, setFormData] = useState<FormData>({
    name: "",
    email: "",
    phone: "",
    amount: "",
    bankStatements: [],
    additionalFiles: []
  });

  const [errors, setErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    // Name validation
    if (!formData.name.trim()) {
      newErrors.name = "Name is required";
    } else if (formData.name.trim().length < 2) {
      newErrors.name = "Name must be at least 2 characters";
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!formData.email.trim()) {
      newErrors.email = "Email is required";
    } else if (!emailRegex.test(formData.email)) {
      newErrors.email = "Please enter a valid email address";
    }

    // Phone validation
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    if (!formData.phone.trim()) {
      newErrors.phone = "Phone number is required";
    } else if (!phoneRegex.test(formData.phone.replace(/[\s\-\(\)]/g, ""))) {
      newErrors.phone = "Please enter a valid phone number";
    }

    // Amount validation
    if (!formData.amount.trim()) {
      newErrors.amount = "Funding amount is required";
    } else {
      const amount = parseFloat(formData.amount.replace(/[,$]/g, ""));
      if (isNaN(amount) || amount < 5000) {
        newErrors.amount = "Minimum funding amount is $5,000";
      } else if (amount > 5000000) {
        newErrors.amount = "Maximum funding amount is $5,000,000";
      }
    }

    // Bank statements validation
    if (formData.bankStatements.length === 0) {
      newErrors.bankStatements = "4 months of bank statements are required";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Handle successful submission
      console.log("Form submitted:", formData);
      
      // Reset form
      setFormData({
        name: "",
        email: "",
        phone: "",
        amount: "",
        bankStatements: [],
        additionalFiles: []
      });
      
      // Show success message (you can implement toast notification here)
      alert("Application submitted successfully! We'll contact you within 1-3 hours.");
      
    } catch (error) {
      console.error("Submission error:", error);
      alert("There was an error submitting your application. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error when user starts typing
    if (errors[field as keyof FormErrors]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const handleFileChange = (field: 'bankStatements' | 'additionalFiles', files: File[]) => {
    setFormData(prev => ({ ...prev, [field]: files }));
    
    // Clear error when files are added
    if (field === 'bankStatements' && errors.bankStatements) {
      setErrors(prev => ({ ...prev, bankStatements: undefined }));
    }
  };

  return (
    <div className="w-full max-w-4xl mx-auto p-4">
      <m.div
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <Card className="backdrop-blur-md bg-white/90 dark:bg-slate-800/90 border border-gray-200/50 dark:border-slate-700/50 shadow-2xl">
          <CardHeader className="text-center pb-2">
            <div className="w-full">
              <p className="text-sm font-medium text-gray-500 dark:text-slate-400 uppercase tracking-wider mb-2">
                CONTACT US
              </p>
              <h2 className="text-2xl lg:text-3xl font-bold text-gray-900 dark:text-white">
                Contact Us and Get Your Approval in 1 to 3 Hours
              </h2>
            </div>
          </CardHeader>
          
          <Divider className="bg-gradient-to-r from-transparent via-gray-300 dark:via-slate-600 to-transparent" />
          
          <CardBody className="p-6 lg:p-8">
            <Form onSubmit={handleSubmit} className="space-y-6">
              {/* Name Field */}
              <m.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: 0.1 }}
              >
                <Input
                  label="Name"
                  placeholder="Enter your full name"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  isRequired
                  isInvalid={!!errors.name}
                  errorMessage={errors.name}
                  startContent={
                    <Icon icon="solar:user-bold" className="text-gray-400 dark:text-slate-500" />
                  }
                  classNames={{
                    input: "text-gray-900 dark:text-white",
                    inputWrapper: "bg-white/50 dark:bg-slate-700/50 border border-gray-200 dark:border-slate-600 hover:border-cyan-400 dark:hover:border-cyan-400 focus-within:border-cyan-500 dark:focus-within:border-cyan-500",
                    label: "text-gray-700 dark:text-slate-300"
                  }}
                />
              </m.div>

              {/* Email Field */}
              <m.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
              >
                <Input
                  type="email"
                  label="Email"
                  placeholder="Enter your email address"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  isRequired
                  isInvalid={!!errors.email}
                  errorMessage={errors.email}
                  startContent={
                    <Icon icon="solar:letter-bold" className="text-gray-400 dark:text-slate-500" />
                  }
                  classNames={{
                    input: "text-gray-900 dark:text-white",
                    inputWrapper: "bg-white/50 dark:bg-slate-700/50 border border-gray-200 dark:border-slate-600 hover:border-cyan-400 dark:hover:border-cyan-400 focus-within:border-cyan-500 dark:focus-within:border-cyan-500",
                    label: "text-gray-700 dark:text-slate-300"
                  }}
                />
              </m.div>

              {/* Phone Field */}
              <m.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: 0.3 }}
              >
                <Input
                  type="tel"
                  label="Phone Number"
                  placeholder="Enter your phone number"
                  value={formData.phone}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                  isRequired
                  isInvalid={!!errors.phone}
                  errorMessage={errors.phone}
                  startContent={
                    <Icon icon="solar:phone-bold" className="text-gray-400 dark:text-slate-500" />
                  }
                  classNames={{
                    input: "text-gray-900 dark:text-white",
                    inputWrapper: "bg-white/50 dark:bg-slate-700/50 border border-gray-200 dark:border-slate-600 hover:border-cyan-400 dark:hover:border-cyan-400 focus-within:border-cyan-500 dark:focus-within:border-cyan-500",
                    label: "text-gray-700 dark:text-slate-300"
                  }}
                />
              </m.div>

              {/* Amount Field */}
              <m.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: 0.4 }}
              >
                <CurrencyInput
                  label="How Much You Need"
                  placeholder="Enter funding amount"
                  value={formData.amount}
                  onChange={(value) => handleInputChange('amount', value)}
                  isRequired
                  isInvalid={!!errors.amount}
                  errorMessage={errors.amount}
                />
              </m.div>

              {/* Bank Statements Upload */}
              <m.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: 0.5 }}
              >
                <FileUpload
                  label="4 Months Bank Statements"
                  description="Upload your last 4 months of bank statements (PDF, JPG, PNG)"
                  files={formData.bankStatements}
                  onChange={(files) => handleFileChange('bankStatements', files)}
                  accept=".pdf,.jpg,.jpeg,.png"
                  multiple
                  isRequired
                  isInvalid={!!errors.bankStatements}
                  errorMessage={errors.bankStatements}
                />
              </m.div>

              {/* Additional Files Upload */}
              <m.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: 0.6 }}
              >
                <FileUpload
                  label="Attach Files"
                  description="Upload any additional documents (Optional)"
                  files={formData.additionalFiles}
                  onChange={(files) => handleFileChange('additionalFiles', files)}
                  accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                  multiple
                />
              </m.div>

              {/* Submit Button */}
              <m.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.7 }}
                className="pt-4"
              >
                <Button
                  type="submit"
                  size="lg"
                  isLoading={isSubmitting}
                  className="w-full h-14 bg-gradient-to-r from-cyan-500 to-cyan-600 hover:from-cyan-600 hover:to-cyan-700 text-white font-semibold text-lg shadow-lg hover:shadow-cyan-500/30 transition-all duration-300"
                  startContent={
                    !isSubmitting && <Icon icon="solar:paper-plane-bold" className="text-xl" />
                  }
                >
                  {isSubmitting ? "Submitting Application..." : "SEND"}
                </Button>
              </m.div>
            </Form>
          </CardBody>
        </Card>
      </m.div>
    </div>
  );
}
