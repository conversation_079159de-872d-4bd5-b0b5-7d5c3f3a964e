"use client";

import React, { useState, useEffect } from "react";
import { Input } from "@heroui/react";
import { Icon } from "@iconify/react";

interface CurrencyInputProps {
  label: string;
  placeholder?: string;
  value: string;
  onChange: (value: string) => void;
  isRequired?: boolean;
  isInvalid?: boolean;
  errorMessage?: string;
  min?: number;
  max?: number;
}

export default function CurrencyInput({
  label,
  placeholder = "Enter amount",
  value,
  onChange,
  isRequired = false,
  isInvalid = false,
  errorMessage,
  min = 5000,
  max = 5000000
}: CurrencyInputProps) {
  const [displayValue, setDisplayValue] = useState("");

  // Format number as currency
  const formatCurrency = (num: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(num);
  };

  // Parse currency string to number
  const parseCurrency = (str: string): number => {
    const cleaned = str.replace(/[^0-9]/g, '');
    return parseInt(cleaned) || 0;
  };

  // Update display value when prop value changes
  useEffect(() => {
    if (value) {
      const numValue = parseCurrency(value);
      if (numValue > 0) {
        setDisplayValue(formatCurrency(numValue));
      } else {
        setDisplayValue("");
      }
    } else {
      setDisplayValue("");
    }
  }, [value]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value;
    
    // Remove all non-numeric characters
    const numericValue = inputValue.replace(/[^0-9]/g, '');
    
    if (numericValue === '') {
      setDisplayValue('');
      onChange('');
      return;
    }

    const num = parseInt(numericValue);
    
    // Apply min/max constraints
    const constrainedNum = Math.min(Math.max(num, 0), max);
    
    // Format for display
    const formatted = formatCurrency(constrainedNum);
    setDisplayValue(formatted);
    
    // Pass raw number string to parent
    onChange(constrainedNum.toString());
  };

  const handleFocus = (e: React.FocusEvent<HTMLInputElement>) => {
    // Show raw number when focused for easier editing
    if (value) {
      const num = parseCurrency(value);
      if (num > 0) {
        setDisplayValue(num.toString());
      }
    }
  };

  const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    // Format as currency when focus is lost
    if (value) {
      const num = parseCurrency(value);
      if (num > 0) {
        setDisplayValue(formatCurrency(num));
      }
    }
  };

  return (
    <div className="w-full">
      <Input
        label={label}
        placeholder={placeholder}
        value={displayValue}
        onChange={handleInputChange}
        onFocus={handleFocus}
        onBlur={handleBlur}
        isRequired={isRequired}
        isInvalid={isInvalid}
        errorMessage={errorMessage}
        startContent={
          <Icon icon="solar:dollar-minimalistic-bold" className="text-gray-400 dark:text-slate-500" />
        }
        endContent={
          <div className="text-xs text-gray-500 dark:text-slate-400">
            USD
          </div>
        }
        classNames={{
          input: "text-gray-900 dark:text-white",
          inputWrapper: "bg-white/50 dark:bg-slate-700/50 border border-gray-200 dark:border-slate-600 hover:border-cyan-400 dark:hover:border-cyan-400 focus-within:border-cyan-500 dark:focus-within:border-cyan-500",
          label: "text-gray-700 dark:text-slate-300"
        }}
        description={
          <div className="text-xs text-gray-500 dark:text-slate-400 mt-1">
            Minimum: {formatCurrency(min)} • Maximum: {formatCurrency(max)}
          </div>
        }
      />
    </div>
  );
}
